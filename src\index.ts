import express, { Request, Response } from "express";
import axios from "axios";
import { Client, LocalAuth, Message, MessageMedia } from "whatsapp-web.js";
import qrcode from "qrcode-terminal";
import Groq from "groq-sdk";
import 'dotenv/config';

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY!,
});

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const port = 5000;

async function mediaToGroqPart(media: MessageMedia) {
  return {
    type: "image_url",
    image_url: {
      url: `data:${media.mimetype};base64,${media.data}`,
    },
  };
}

const whatsappClient = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {
    args: ['--no-sandbox', '--disable-setuid-sandbox'], 
  },
});

whatsappClient.on("qr", (qr: string) => {
  qrcode.generate(qr, { small: true });
  console.log("QR Code received, scan with your phone.");
});

whatsappClient.on("ready", () => {
  console.log("WhatsApp Web client is ready!");
});

whatsappClient.on("message", async (msg: Message) => {
  const senderNumber: string = msg.from;
  const message: string = msg.body;

  console.log(`Received message from ${senderNumber}: ${message}`);

  let mediaPart = null;

  if (msg.hasMedia) {
    const media = await msg.downloadMedia();
    mediaPart = await mediaToGroqPart(media);
  }

  await run(message, senderNumber, mediaPart);
});

whatsappClient.initialize();

// Store conversation history for each user
const conversationHistory: { [key: string]: any[] } = {};

async function run(message: string, senderNumber: string, mediaPart?: any): Promise<void> {
  try {
    // Initialize conversation history for new users
    if (!conversationHistory[senderNumber]) {
      conversationHistory[senderNumber] = [];
    }

    // Build the message content
    let content: any[] = [{ type: "text", text: message }];

    if (mediaPart) {
      content.push(mediaPart);
    }

    // Add user message to conversation history
    conversationHistory[senderNumber].push({
      role: "user",
      content: content
    });

    // Keep only last 10 messages to avoid token limit issues
    if (conversationHistory[senderNumber].length > 10) {
      conversationHistory[senderNumber] = conversationHistory[senderNumber].slice(-10);
    }

    const completion = await groq.chat.completions.create({
      model: "meta-llama/llama-4-maverick-17b-128e-instruct",
      messages: conversationHistory[senderNumber],
      max_tokens: 6000,
      temperature: 0.7,
    });

    const text = completion.choices[0]?.message?.content;

    if (text) {
      console.log("Generated Text:", text);

      // Add assistant response to conversation history
      conversationHistory[senderNumber].push({
        role: "assistant",
        content: text
      });

      await sendWhatsAppMessage(text, senderNumber);
    } else {
      console.error("No response generated from Groq API");
      await sendWhatsAppMessage("Sorry, I couldn't generate a response. Please try again.", senderNumber);
    }

  } catch (error) {
    console.error("Error in run function:", error);
    await sendWhatsAppMessage("Oops, an error occurred. Please try again later.", senderNumber);
  }
}

async function sendWhatsAppMessage(text: string, toNumber: string): Promise<void> {
  try {
    await whatsappClient.sendMessage(toNumber, text);
  } catch (err) {
    console.error("Failed to send WhatsApp message:");
    console.error("Error details:", err);
  }
}

app.listen(port, () => console.log(`Express app running on port ${port}!`));
